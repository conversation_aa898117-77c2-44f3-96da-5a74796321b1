import { QuestionCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { Spin, Tooltip } from 'antd';
import { useMemo } from 'react';

import { IMessageItem4Render } from '@/types';
import { MarkdownRenderer } from '../markdown-renderer';
// import ThoughtChain from '../thought-chain';
import WorkflowLogs from './workflow-logs';

interface IMessageContentProps {
  /**
   * 提交消息时触发的回调函数
   * @param nextContent 下一条消息的内容
   * @param files 附件文件列表
   */
  onSubmit: (value: string) => void;
  /**
   * 消息数据对象
   */
  messageItem: IMessageItem4Render;
}

/**
 * 根据消息项的事件类型和顺序显示内容
 * @param props 组件属性
 * @returns 渲染的消息内容组件
 */
export default function MessageContent(props: IMessageContentProps) {
  const {
    // onSubmit,
    messageItem: {
      // id,
      status,
      error,
      // agentThoughts,
      workflows,
      // files,
      content,
      // retrieverResources,
      role,
      isHistory,
      contentBlocks = [],
    },
  } = props;

  const computedContent = useMemo(() => {
    return content || '';
  }, [content, role]);

  // 如果是错误状态，则直接展示错误信息
  if (status === 'error') {
    return (
      <p className="text-red-700">
        <WarningOutlined className="mr-2" />
        <span>{error}</span>
      </p>
    );
  }

  // 如果状态正常且没有任何数据，则展示缺省
  if (
    status === 'success' &&
    !content &&
    // !files?.length &&
    // !agentThoughts?.length &&
    !workflows?.nodes?.length &&
    // !retrieverResources?.length &&
    !contentBlocks.length
  ) {
    return (
      <p className="text-orange-600">
        <WarningOutlined className="mr-2" />
        <span>消息内容为空</span>
        <Tooltip title="可能是用户在生成内容的过程中点击了停止响应按钮">
          <QuestionCircleOutlined className="ml-2" />
        </Tooltip>
      </p>
    );
  }
  console.log('contentBlocks=============', contentBlocks);

  // 如果有content blocks则按照块的顺序展示
  if (contentBlocks?.length) {
    const animate = status === 'loading';
    return (
      <>
        {contentBlocks.map((block: any, index: number) => {
          // 根据块类型渲染相应的组件
          let blockNode = null;
          switch (block.type) {
            // case 'agent_thought':
            //   blockNode = (
            //     <ThoughtChain
            //       key={`thought-${index}`}
            //       uniqueKey={`${id}-thought-${index}`}
            //       items={block.agentThoughts}
            //       className="mt-3"
            //       animate={animate}
            //     />
            //   );
            //   break;
            case 'workflow':
              blockNode = (
                <WorkflowLogs
                  key={`workflow-${index}`}
                  items={block.workflows?.nodes || []}
                  status={block.workflows?.status}
                  isHistory={isHistory}
                  animate={animate}
                />
              );
              break;
            // case 'file':
            //   blockNode = (
            //     <div key={`file-${index}`} className="mt-3">
            //       <MessageFileList files={block.files} animate={animate} />
            //     </div>
            //   );
            //   break;
            case 'content':
              blockNode = (
                <div
                  key={`content-${index}`}
                  className={role === 'local' || role === 'user' ? '' : 'md:min-w-chat-card'}
                >
                  <MarkdownRenderer markdownText={block.content} />
                </div>
              );
              break;
            // case 'referrence':
            //   blockNode = (
            //     <MessageReferrence
            //       key={`ref-${index}`}
            //       items={block.retrieverResources}
            //       animate={animate}
            //     />
            //   );
            //   break;
            default:
              blockNode = null;
          }
          // 包裹动画容器
          // className={animate && styles['fade-in']}
          return <div key={blockNode?.key || index}>{blockNode}</div>;
        })}
        {status !== 'success' && (
          <div className="my-2 text-[#999] flex items-center">
            <Spin size="small" className="mr-2" />
            正在思考中...
          </div>
        )}
      </>
    );
  }

  return (
    <>
      {/* Agent 思维链信息 */}
      {/* <ThoughtChain uniqueKey={id as string} items={agentThoughts} className="mt-3" /> */}

      {/* 工作流执行日志 */}
      {/* <WorkflowLogs items={workflows?.nodes || []} status={workflows?.status} /> */}

      {/* 消息附件列表 */}
      {/* {files?.length ? (
        <div className="mt-3">
          <MessageFileList files={files} />
        </div>
      ) : null} */}

      {/* 消息主体文本内容 */}
      <div className={role === 'local' || role === 'user' ? '' : 'md:min-w-chat-card'}>
        <MarkdownRenderer markdownText={computedContent} />
      </div>

      {/* 引用链接列表 */}
      {/* <MessageReferrence items={retrieverResources} /> */}
    </>
  );
}
