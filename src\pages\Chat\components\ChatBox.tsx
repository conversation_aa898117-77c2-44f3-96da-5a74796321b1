import { UserOutlined } from '@ant-design/icons';
import { Bubble, Prompts } from '@ant-design/x';
import { GetProp, Space, Spin } from 'antd';
import { useDeferredValue, useEffect, useMemo, useRef, useState } from 'react';

import { IGetAppParametersResponse } from '../type';
import { MessageSender } from './MessageSender';
import { WelcomePlaceholder } from './WelcomePlaceholder';
// import MessageFooter from './message/footer';
import { RoleType } from '@/enums';
import chat from '@/services/chat';
import { ChatAPI } from '@/services/chat/typeings';
import AiLogo from '@/static/images/aiLogo.png';
import { IMessageFileItem, IMessageItem4Render } from '@/types';
import { isTempId } from '@/utils';
import { createStyles } from 'antd-style';
import MessageContent from './message/content';
import MessageFooter from './message/footer';

export interface ChatboxProps {
  /**
   * 应用参数
   */
  appParameters?: IGetAppParametersResponse;
  /**
   * 消息列表
   */
  messageItems: IMessageItem4Render[];
  /**
   * 是否正在请求
   */
  isRequesting: boolean;
  /**
   * 内容提交事件
   * @param value 问题-文本
   * @param files 问题-文件
   */
  onSubmit: (
    value: string,
    options?: {
      files?: any[];
      inputs?: Record<string, unknown>;
    },
  ) => void;
  /**
   * 取消读取流
   */
  onCancel: () => void;
  /**
   * 反馈执行成功后的回调
   */
  feedbackCallback?: (conversationId: string) => void;
  /**
   * 对话 ID
   */
  conversationId?: string;
}

const useStyle = createStyles(({ css }) => {
  return {
    loadingMessage: css`
      background-image: linear-gradient(90deg, #ff6b23 0%, #af3cb8 31%, #53b6ff 89%);
      background-size: 100% 2px;
      background-repeat: no-repeat;
      background-position: bottom;
    `,
  };
});

/**
 * 判断滚动容器是否在底部
 */
const isScrollAtBottom = (container: HTMLElement, threshold = 10): boolean => {
  const { scrollTop, scrollHeight, clientHeight } = container;
  return scrollHeight - scrollTop - clientHeight <= threshold;
};

/**
 * 对话内容区
 */
export const Chatbox = (props: ChatboxProps) => {
  const {
    messageItems,
    isRequesting,
    onSubmit,
    onCancel,
    conversationId,
    appParameters,
    feedbackCallback,
  } = props;
  const [content, setContent] = useState('');
  // 是否应该自动滚动到底部
  const [shouldAutoScroll, setShouldAutoScroll] = useState<boolean>(true);

  const { styles } = useStyle();

  const roles: GetProp<typeof Bubble.List, 'roles'> = {
    ai: {
      placement: 'start',
      avatar: {
        src: <img src={AiLogo} alt="AI Logo" />,
        style: { background: '#ffffff', border: '1px solid #F0F0F0' },
      },
      style: {
        // 减去一个头像的宽度
        maxWidth: 'calc(100% - 44px)',
      },
      loadingRender: () => (
        <Space className="my-[8px]">
          <Spin size="small" />
          loading...
        </Space>
      ),
    },
    user: {
      placement: 'end',
      avatar: {
        icon: <UserOutlined />,
        style: {
          background: '#87d068',
        },
      },
      style: {
        // 减去一个头像的宽度
        maxWidth: 'calc(100% - 44px)',
        marginLeft: '44px',
      },
    },
  };

  const items: GetProp<typeof Bubble.List, 'items'> = useMemo(() => {
    return messageItems?.map((messageItem) => {
      return {
        key: `${messageItem.id}-${messageItem.role}`,
        classNames: {
          content: messageItem.status === 'loading' ? styles.loadingMessage : '',
        },
        // 不要开启 loading 和 typing, 否则流式会无效
        loading: messageItem.requestStatus === 'loading',
        typing: messageItem.status === 'loading' ? { step: 5, interval: 20, suffix: '...' } : false,
        messageRender: () => {
          return <MessageContent onSubmit={onSubmit} messageItem={messageItem} />;
        },
        // 用户发送消息时，status 为 local，需要展示为用户头像
        role: messageItem.role === RoleType.local ? RoleType.user : messageItem.role,
        footer: messageItem.status !== 'loading' && (
          <div className="flex items-center">
            <MessageFooter
              feedbackApi={(params) => {
                console.log('feedbackApi:', params);
                const { messageId, rating, content } = params;
                return chat.api.comment({
                  intention_id: Number(messageId),
                  is_good: rating,
                  user_comment: content,
                });
              }}
              messageId={messageItem.id}
              messageContent={messageItem.content}
              role={messageItem.role}
              feedback={{
                rating: messageItem.feedback?.rating,
                callback: () => {
                  feedbackCallback?.(conversationId!);
                },
              }}
            />
            {messageItem.role === RoleType.ai && messageItem.created_at && (
              <div className="ml-3 text-sm text-desc">回复时间：{messageItem.created_at}</div>
            )}
          </div>
        ),
      };
    }) as GetProp<typeof Bubble.List, 'items'>;
  }, [messageItems, conversationId, onSubmit]);

  const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
    setContent((info.data as ChatAPI.AgentList).prompt_template);
  };

  const handleSubmit = (
    content: string,
    options?: {
      files?: IMessageFileItem[];
    },
  ) => {
    if (!content) return;

    // 提交消息时重置滚动状态
    onSubmit(content, options);
    setContent('');
    setShouldAutoScroll(true);
  };

  // 监听滚动事件 - 检测用户手动滚动
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      if (isScrollAtBottom(scrollContainer)) {
        setShouldAutoScroll(true);
      } else {
        setShouldAutoScroll(false);
      }
    };

    scrollContainer.addEventListener('scroll', handleScroll);
    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
    };
  }, [shouldAutoScroll]);

  // 监听 items 更新，确保首次加载时滚动到底部
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const deferredItems = useDeferredValue(items);

  useEffect(() => {
    if (!scrollContainerRef.current || !shouldAutoScroll) return;
    // 直接使用scrollTop设置，不使用smooth行为以避免冲突
    // scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    scrollContainerRef.current.scrollTo({
      behavior: 'smooth',
      top: scrollContainerRef.current.scrollHeight,
    });
  }, [deferredItems, shouldAutoScroll]);

  return (
    <div className="w-full h-full my-0 mx-auto box-border flex flex-col gap-4 bg-white">
      <div className="flex-1 overflow-auto" ref={scrollContainerRef}>
        {/* 欢迎占位 */}
        {!items?.length && isTempId(conversationId) ? (
          <div className="m-auto max-w-[1000px]">
            <WelcomePlaceholder
              appParameters={appParameters}
              onPromptItemClick={(info) => {
                onPromptsItemClick(info);
                setShouldAutoScroll(true);
              }}
            />
          </div>
        ) : (
          <Bubble.List className="px-[calc(50%_-_500px)]" items={items} roles={roles} />
        )}
      </div>
      <div className="bg-white w-full max-w-[1000px] m-auto">
        {/* 输入框 */}
        <MessageSender
          className="w-full px-3"
          content={content}
          setContent={setContent}
          onChange={(value) => setContent(value)}
          onSubmit={handleSubmit}
          isRequesting={isRequesting}
          uploadFileApi={(params, options) => {
            console.log('uploadFileApi:', params);
            return chat.api.uploadFile(params, options);
          }}
          onCancel={onCancel}
          conversationId={conversationId}
        />
        <div className="text-gray-400 text-sm text-center h-8 leading-8">
          内容由 AI 生成, 仅供参考
        </div>
      </div>
    </div>
  );
};
