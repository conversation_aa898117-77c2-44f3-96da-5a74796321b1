import { UserOutlined } from '@ant-design/icons';
import { Bubble, Prompts } from '@ant-design/x';
import { GetProp, Space, Spin } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { IGetAppParametersResponse } from '../type';
import { MessageSender } from './MessageSender';
import { WelcomePlaceholder } from './WelcomePlaceholder';
// import MessageFooter from './message/footer';
import { RoleType } from '@/enums';
import chat from '@/services/chat';
import { ChatAPI } from '@/services/chat/typeings';
import AiLogo from '@/static/images/aiLogo.png';
import { IMessageFileItem, IMessageItem4Render } from '@/types';
import { isTempId } from '@/utils';
import { createStyles } from 'antd-style';
import MessageContent from './message/content';
import MessageFooter from './message/footer';

export interface ChatboxProps {
  /**
   * 应用参数
   */
  appParameters?: IGetAppParametersResponse;
  /**
   * 消息列表
   */
  messageItems: IMessageItem4Render[];
  /**
   * 是否正在请求
   */
  isRequesting: boolean;
  /**
   * 内容提交事件
   * @param value 问题-文本
   * @param files 问题-文件
   */
  onSubmit: (
    value: string,
    options?: {
      files?: any[];
      inputs?: Record<string, unknown>;
    },
  ) => void;
  /**
   * 取消读取流
   */
  onCancel: () => void;
  /**
   * 反馈执行成功后的回调
   */
  feedbackCallback?: (conversationId: string) => void;
  /**
   * 对话 ID
   */
  conversationId?: string;
}

const useStyle = createStyles(({ css }) => {
  return {
    loadingMessage: css`
      background-image: linear-gradient(90deg, #ff6b23 0%, #af3cb8 31%, #53b6ff 89%);
      background-size: 100% 2px;
      background-repeat: no-repeat;
      background-position: bottom;
    `,
  };
});

/**
 * 判断滚动容器是否在底部
 */
const isScrollAtBottom = (container: HTMLElement, threshold = 20): boolean => {
  const { scrollTop, scrollHeight, clientHeight } = container;
  return scrollHeight - scrollTop - clientHeight <= threshold;
};

/**
 * 对话内容区
 */
export const Chatbox = (props: ChatboxProps) => {
  const {
    messageItems,
    isRequesting,
    onSubmit,
    onCancel,
    conversationId,
    appParameters,
    feedbackCallback,
  } = props;
  const [content, setContent] = useState('');
  // 是否应该自动滚动到底部
  const [shouldAutoScroll, setShouldAutoScroll] = useState<boolean>(true);
  // 滚动容器引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  // 滚动定时器引用
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  // 是否正在执行平滑滚动
  const isSmoothScrollingRef = useRef<boolean>(false);
  // 上次滚动的高度
  const lastScrollHeightRef = useRef<number>(0);

  const { styles } = useStyle();

  const roles: GetProp<typeof Bubble.List, 'roles'> = {
    ai: {
      placement: 'start',
      avatar: {
        src: <img src={AiLogo} alt="AI Logo" />,
        style: { background: '#ffffff', border: '1px solid #F0F0F0' },
      },
      style: {
        // 减去一个头像的宽度
        maxWidth: 'calc(100% - 44px)',
      },
      loadingRender: () => (
        <Space className="my-[8px]">
          <Spin size="small" />
          loading...
        </Space>
      ),
    },
    user: {
      placement: 'end',
      avatar: {
        icon: <UserOutlined />,
        style: {
          background: '#87d068',
        },
      },
      style: {
        // 减去一个头像的宽度
        maxWidth: 'calc(100% - 44px)',
        marginLeft: '44px',
      },
    },
  };

  const items: GetProp<typeof Bubble.List, 'items'> = useMemo(() => {
    return messageItems?.map((messageItem) => {
      return {
        key: `${messageItem.id}-${messageItem.role}`,
        classNames: {
          content: messageItem.status === 'loading' ? styles.loadingMessage : '',
        },
        // 不要开启 loading 和 typing, 否则流式会无效
        loading: messageItem.requestStatus === 'loading',
        typing: messageItem.status === 'loading' ? { step: 5, interval: 20, suffix: '...' } : false,
        messageRender: () => {
          return <MessageContent onSubmit={onSubmit} messageItem={messageItem} />;
        },
        // 用户发送消息时，status 为 local，需要展示为用户头像
        role: messageItem.role === RoleType.local ? RoleType.user : messageItem.role,
        footer: messageItem.status !== 'loading' && (
          <div className="flex items-center">
            <MessageFooter
              feedbackApi={(params) => {
                console.log('feedbackApi:', params);
                const { messageId, rating, content } = params;
                return chat.api.comment({
                  intention_id: Number(messageId),
                  is_good: rating,
                  user_comment: content,
                });
              }}
              messageId={messageItem.id}
              messageContent={messageItem.content}
              role={messageItem.role}
              feedback={{
                rating: messageItem.feedback?.rating,
                callback: () => {
                  feedbackCallback?.(conversationId!);
                },
              }}
            />
            {messageItem.role === RoleType.ai && messageItem.created_at && (
              <div className="ml-3 text-sm text-desc">回复时间：{messageItem.created_at}</div>
            )}
          </div>
        ),
      };
    }) as GetProp<typeof Bubble.List, 'items'>;
  }, [messageItems, conversationId, onSubmit]);

  const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
    setContent((info.data as ChatAPI.AgentList).prompt_template);
  };

  const handleSubmit = (
    content: string,
    options?: {
      files?: IMessageFileItem[];
    },
  ) => {
    if (!content) return;

    // 提交消息时重置滚动状态
    onSubmit(content, options);
    setContent('');
    setShouldAutoScroll(true);
  };

  // 监听滚动事件 - 检测用户手动滚动
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      // 如果正在执行平滑滚动，忽略滚动事件
      if (isSmoothScrollingRef.current) return;

      // 清除之前的定时器，避免频繁检测
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // 延迟检测，避免在快速滚动时频繁触发
      scrollTimeoutRef.current = setTimeout(() => {
        // 再次检查是否在平滑滚动中
        if (isSmoothScrollingRef.current) return;

        const isAtBottom = isScrollAtBottom(scrollContainer, 50);

        if (isAtBottom && !shouldAutoScroll) {
          // 用户滚动回底部，恢复自动滚动
          setShouldAutoScroll(true);
        } else if (!isAtBottom && shouldAutoScroll) {
          // 用户手动滚动离开底部，停止自动滚动
          setShouldAutoScroll(false);
        }
      }, 150); // 延长延迟时间，给平滑滚动更多时间
    };

    scrollContainer.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [shouldAutoScroll]);

  // 智能平滑滚动函数
  const scrollToBottom = useCallback((force = false) => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const currentHeight = scrollContainer.scrollHeight;
    const lastHeight = lastScrollHeightRef.current;

    // 计算高度变化量
    const heightDiff = currentHeight - lastHeight;

    // 如果高度变化很大（超过2个屏幕高度），使用立即滚动
    const shouldUseInstantScroll = heightDiff > scrollContainer.clientHeight * 2;

    if (force || shouldUseInstantScroll) {
      // 立即滚动，用于大量内容变化
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
      lastScrollHeightRef.current = currentHeight;
    } else {
      // 平滑滚动，用于正常情况
      isSmoothScrollingRef.current = true;

      scrollContainer.scrollTo({
        behavior: 'smooth',
        top: scrollContainer.scrollHeight,
      });

      // 平滑滚动完成后重置状态
      setTimeout(() => {
        isSmoothScrollingRef.current = false;
        lastScrollHeightRef.current = scrollContainer.scrollHeight;
      }, 600); // 给平滑滚动足够的时间
    }
  }, []);

  // 监听消息变化，在应该自动滚动时执行滚动
  useEffect(() => {
    if (!shouldAutoScroll) return;

    // 使用 requestAnimationFrame 确保 DOM 更新完成后再滚动
    requestAnimationFrame(() => {
      scrollToBottom();
    });
  }, [items, shouldAutoScroll, scrollToBottom]);

  // 使用 ResizeObserver 监听容器高度变化 - 配合平滑滚动
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer || !shouldAutoScroll) return;

    let resizeTimer: NodeJS.Timeout | null = null;
    let lastHeight = scrollContainer.scrollHeight;

    const resizeObserver = new ResizeObserver(() => {
      // 清除之前的定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }

      // 延迟处理，避免频繁触发
      resizeTimer = setTimeout(() => {
        const currentHeight = scrollContainer.scrollHeight;

        // 只有当高度真正发生变化时才滚动
        if (currentHeight !== lastHeight && shouldAutoScroll) {
          const heightDiff = currentHeight - lastHeight;
          lastHeight = currentHeight;

          // 检查当前是否在底部附近
          const isNearBottom = isScrollAtBottom(scrollContainer, 100);

          if (isNearBottom) {
            // 根据高度变化量决定滚动方式
            const shouldForceInstant = heightDiff > scrollContainer.clientHeight * 1.5;
            scrollToBottom(shouldForceInstant);
          }
        }
      }, 100); // 适中的延迟时间
    });

    // 监听滚动容器的尺寸变化
    resizeObserver.observe(scrollContainer);

    return () => {
      resizeObserver.disconnect();
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }
    };
  }, [shouldAutoScroll, scrollToBottom]);

  // 组件卸载时清理所有定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="w-full h-full my-0 mx-auto box-border flex flex-col gap-4 bg-white">
      <div className="flex-1 overflow-auto" ref={scrollContainerRef}>
        {/* 欢迎占位 */}
        {!items?.length && isTempId(conversationId) ? (
          <div className="m-auto max-w-[1000px]">
            <WelcomePlaceholder
              appParameters={appParameters}
              onPromptItemClick={(info) => {
                onPromptsItemClick(info);
                setShouldAutoScroll(true);
              }}
            />
          </div>
        ) : (
          <Bubble.List className="px-[calc(50%_-_500px)]" items={items} roles={roles} />
        )}
      </div>
      <div className="bg-white w-full max-w-[1000px] m-auto">
        {/* 输入框 */}
        <MessageSender
          className="w-full px-3"
          content={content}
          setContent={setContent}
          onChange={(value) => setContent(value)}
          onSubmit={handleSubmit}
          isRequesting={isRequesting}
          uploadFileApi={(params, options) => {
            console.log('uploadFileApi:', params);
            return chat.api.uploadFile(params, options);
          }}
          onCancel={onCancel}
          conversationId={conversationId}
        />
        <div className="text-gray-400 text-sm text-center h-8 leading-8">
          内容由 AI 生成, 仅供参考
        </div>
      </div>
    </div>
  );
};
